<template>
  <div class="facility-header">
    {{ name }}
    <span class="caption" v-if="isGameFormationEnabled && formation && formation.length">
      ({{ gameFormations() }})
    </span>
    <span class="facility-online-badge caption" v-if="isServiceEnableOnline && checkWritePermission($modules.schedule.availability.slug)">
      <v-btn icon class="facility-online-btn" v-if="facilityOnlineDisabled" @click="onlineEnableToggle(facilityId, 0)">
        <v-icon :color="'#df5716'">mdi-lightning-bolt-circle</v-icon>
      </v-btn>
      <v-btn icon class="facility-online-btn" v-else @click="onlineEnableToggle(facilityId, isPublic)">
        <v-icon :color="isPublic == 1 ? 'success' : '#df5716'">mdi-lightning-bolt-circle</v-icon>
      </v-btn>
    </span>
    <span class="c-booking-btn" v-if="perCapacity">
      <v-btn
          color="black"
          x-small
          dark
          outlined
          block
          @click="showTotalAttendees"
      >Total {{ totalAttendees }}{{isEnablePerDayCapacity?' / '+perDayCapacity:''}}
      </v-btn>
    </span>
  </div>
</template>
<script>
export default {
  name: 'FacilityHeaderCell',
  props: {
    perCapacity: { type: Number, default: 0 },
    name: { type: String, default: "Pitch Name" },
    date:{type :String, default: null},
    isServiceEnableOnline: { type: Number, default: 0 },
    isGameFormationEnabled:{ type:Number, default:1},
    isPublic: { type: Number, default: 1 },
    facilityId: { type: Number, default: 1 },
    formation: { type: Array, default: () => [] },
    tooltip: { type: String },
    facilityOnlineDisabled: { type: Object, default: null },
    totalAttendees: { type: Number, default: 0 },
    totalCapacity: { type: Number, default: 0 },
    perDayCapacity: { type: Number, default: 0 },
    isEnablePerDayCapacity: { type: Number, default: 0 },
  },
  methods: {
    gameFormations() {
      return this.formation.map((item) => item.name).join(",");
    },
    onlineEnableToggle(facility_id, is_public) {
      this.$emit("enableDisableOnlineFacility", {
        facility_id: facility_id,
        is_public: is_public,
        date:this.date
      });
    },
    showTotalAttendees() {
      return this.$emit("showTotalAttendees");
    },
  }
};
</script>
<style scoped>
.facility-header {
  text-align: center;
  border-bottom: 1px solid #ccc;
  padding: 8px 4px;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 3;
  background-color: rgb(233, 241, 246);
  color: black;
  font-weight: 600;
  min-height: 64px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.facility-header span{
  display: block;
}
.facility-online-badge{
  position: absolute;
  top: 0;
  right:0;
}
</style>