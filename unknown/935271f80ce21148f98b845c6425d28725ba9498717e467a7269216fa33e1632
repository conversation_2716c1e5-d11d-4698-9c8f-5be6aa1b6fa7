<template>
  <v-expansion-panels flat multiple v-model="templatePanel" class="ttpc">
    <v-expansion-panel class="rounded-4">
      <v-expansion-panel-header class="main-panel-header font-semibold" color="00B0AF">
        {{ template.name || 'Timing Template Name' }}
        <span class="sync-tt" @click.stop="$emit('sync-template-confirm', index)" title="Sync with facility">
          <v-btn x-small>Sync</v-btn>
        </span>
      </v-expansion-panel-header>

      <v-expansion-panel-content>
        <v-card>
          <v-card-title>
            <v-text-field
                v-model.lazy="template.name"
                :rules="[(v) => !!v || 'Name is required']"
                placeholder="Name"
                outlined
                dense
                required
                class="q-text-field shadow-0 rounded-4"
                hide-details="auto"
            />
            <v-btn icon @click.stop="$emit('remove-template', index)">
              <DeleteIcon stroke="#000000"/>
            </v-btn>
          </v-card-title>

          <v-card-text class="rounded-4">
            <v-expansion-panels multiple>
              <v-expansion-panel
                  v-for="(group, groupIndex) in template.dayGroups"
                  :key="groupIndex"
                  class="mt-2 mb-2 rounded-4 bg-gray"
              >
                <v-expansion-panel-header class="bg-white child-panel-header">
                  <div class="d-flex justify-space-between align-center" style="width: 100%">
                    <span>Selected Days: {{ summarizeDays(group.weekdays) || 'N/A' }}</span>
                    <v-btn icon @click.stop="$emit('remove-day-group', index, groupIndex)" width="16px" height="16px">
                      <DeleteIcon stroke="#000000"/>
                    </v-btn>
                  </div>
                </v-expansion-panel-header>

                <v-expansion-panel-content class="pl-6 pr-6 pt-3 pb-3 rounded-4">
                  <v-chip-group
                      v-model="group.weekdays"
                      multiple
                      column
                      active-class="selected-chip"
                      class="mb-3"
                  >
                    <v-chip
                        v-for="(day, i) in days"
                        :key="i"
                        :value="day.value"
                        outlined
                        filter
                        style="min-width: 56px; justify-content: center;"
                    >
                      {{ day.label }}
                    </v-chip>
                  </v-chip-group>

                  <div
                      v-for="(range, rangeIndex) in group.timeRanges"
                      :key="rangeIndex"
                      class="d-flex align-center mb-2"
                  >
                    <v-select
                        v-model="range.start_time"
                        :items="startTimes"
                        :menu-props="{ bottom: true, offsetY: true }"
                        :rules="[(v) => !!v || 'Start time is required']"
                        background-color="#fff"
                        placeholder="Start Time"
                        class="q-autocomplete-rounded shadow-0 mr-2 rounded-4 equal-width-select"
                        dense
                        hide-details="auto"
                        item-text="text"
                        item-value="value"
                        outlined
                        required
                        prepend-inner-icon="mdi-clock-outline"
                    ></v-select>
                    <v-select
                        v-model="range.end_time"
                        :items="endTimes"
                        :menu-props="{ bottom: true, offsetY: true }"
                        :rules="[(v) => !!v || 'End time is required']"
                        background-color="#fff"
                        placeholder="End Time"
                        class="q-autocomplete-rounded shadow-0 mr-2 rounded-4 equal-width-select"
                        dense
                        hide-details="auto"
                        item-text="text"
                        item-value="value"
                        outlined
                        required
                        prepend-inner-icon="mdi-clock-outline"
                    ></v-select>
                    <v-btn icon @click="$emit('remove-time-range', index, groupIndex, rangeIndex)" class="bg-white">
                      <DeleteIcon stroke="#000000"/>
                    </v-btn>
                  </div>

                  <v-btn small text @click="$emit('add-time-range', index, groupIndex)" color="#00B0AF" class="toCapitalize">
                    + Add Time Range
                  </v-btn>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>

            <v-btn small text class="mt-2 text-neon toCapitalize" @click="$emit('add-day-group', index)">
              + Add Days
            </v-btn>
          </v-card-text>
        </v-card>
      </v-expansion-panel-content>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>
import DeleteIcon from "@/assets/images/misc/delete-icon.svg";
export default {
  name: "FacilityTimingTemplateCard",
  components: { DeleteIcon },
  props: {
    template: Object,
    index: Number,
    days: Array,
    startTimes: Array,
    endTimes: Array,
  },
  data() {
    return {
      templatePanel: [0],
    };
  },
  methods: {
    summarizeDays(bitList) {
      const map = this.days.reduce((acc, day) => {
        acc[day.value] = day.label;
        return acc;
      }, {});
      return bitList.map((bit) => map[bit]).join(", ");
    },
    confirmSync(){

    }
  },
};
</script>

<style scoped>
.equal-width-select {
  width: 180px;
  flex-shrink: 0;
}
.ttpc .v-expansion-panel-header.main-panel-header{
  background: #00B0AF;
  color: #FFFFFF !important;
}
.ttpc .v-expansion-panel-header {
  min-height: 48px;
}
.toCapitalize{
  text-transform: capitalize;
}

.ttpc .v-expansion-panel .v-expansion-panel-header.main-panel-header{
  border-radius: 1rem;
}
.ttpc .v-expansion-panel--active .v-expansion-panel-header.main-panel-header{
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 0rem;
  border-bottom-left-radius: 0rem;
}
.ttpc .v-expansion-panel-header.child-panel-header {
  border-radius: 1rem !important;
}
.ttpc .selected-chip.v-chip.v-chip--outlined.v-chip.v-chip{
  background-color: #f6f8fa !important;
}
.bg-gray{
  background-color: #f6f8fa !important;
}
span.sync-tt {
  color: #fff;
  background: transparent;
  text-align: right;;
  position: absolute;
  right: 60px;
}
.ttpc ::v-deep .v-icon.notranslate.mdi.mdi-chevron-down.theme--light {
  color: #fff !important;
}
</style>