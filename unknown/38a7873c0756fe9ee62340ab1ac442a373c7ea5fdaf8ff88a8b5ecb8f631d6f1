<template>
  <div
      :key="`slot_${facility.id}_${index}`"
      class="slot"
      :style="{ height: getSlotPixelHeight(time) + 'px' }"
      :class="time.status"
      @click.stop="onClickSlot"
      @mouseenter="$emit('hoverTime', time.startMinutes)"
      @mouseleave="$emit('hoverTime', null)"
  >
    <div
        v-if="time.status === 'available' && checkWritePermission($modules.schedule.management.slug)"
        class="slot-plus"
    >
      <div v-if="facility.per_capacity" class="capacity-based">
        <v-btn color="teal" fab x-small dark class="plus-icon">
          <v-icon small>mdi-plus</v-icon>
        </v-btn>
        <v-btn x-small dark outlined class="attendance-box">
          0 <span v-if="!facility.is_enable_per_day_capacity">/ {{ facility.capacity }} </span>
        </v-btn>
      </div>
      <div v-else class="time-based">
        <v-btn color="teal" fab x-small dark class="plus-icon">
          <v-icon small>mdi-plus</v-icon>
        </v-btn>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'FacilityColumn',
  props: {
    globalTimeIncrement: { type: Number, default: 60 },
    index: { type: String, default: "0" },
    facility:{type: Object, default: null},
    time: { type: Object, default: null },
    slotHeight: { type: Number, default: 50 },
  },
  methods: {
    highlight() {
      this.$store.dispatch("highlightTime", this.index);
    },
    removeHighlight() {
      this.$store.dispatch("removeHighlightTime", this.index);
    },
    getSlotPixelHeight(time) {
      const duration = time.endMinutes - time.startMinutes;
      return (duration / this.globalTimeIncrement) * this.slotHeight;
    },
    onClickSlot(){
      console.log("this.facility",this.facility);
      if (!this.checkWritePermission(this.$modules.schedule.management.slug) && !this.time.available){
        console.log("returninnnn");
        return;
      }
      if (this.time.status === 'cart' ||
          this.time.status === "not_available" ||
          this.time.status === "maintenance" ||
          this.time.status === "event" ||
          this.time.status === "completed" ||
          this.time.status === "dependency" ||
          this.time.status === "partial"
      ){
        console.log("return",this.time.status);
        return;
      }
      if(this.time.isOverNight){
        this.showInfo("This is extended booking, If you want to view, Please click on original booking");
        return;
      }

      let slotData = {...this.time};
      if (parseInt(this.facility.per_capacity) === 1 || parseInt(this.time.is_split_payment) === 1) {
        delete slotData.order_id;
        delete slotData.id;
      }
      slotData.capacity = this.facility.capacity;
      slotData.facility_name = this.facility.name;
      slotData.facility_id = parseInt(this.facility.id);
      slotData.per_capacity = parseInt(this.facility.per_capacity);
      slotData.min_booking_time = parseInt(this.facility.min_booking_time);
      slotData.facility_time_increment = parseInt(this.facility.facility_time_increment);
      slotData.opening_time = this.facility.opening_time;
      slotData.closing_time = this.facility.closing_time;
      slotData.current_date = this.facility._date?this.facility._date:null;
      slotData.is_enable_limit_product = this.facility.is_enable_limit_product?this.facility.is_enable_limit_product:0;
      slotData.max_base_product = this.facility.max_base_product?this.facility.max_base_product:null;
      //console.log("slotData",slotData);
      this.$emit("openBookingForm", slotData);
    },
    isBookingTimeValid(startMinutes, endMinutes, availableSlots, minBookingTime) {
      const duration = endMinutes - startMinutes;
      if (duration < minBookingTime) {
        return { valid: false, reason: `Booking duration (${duration} mins) is less than required minimum (${minBookingTime} mins).` };
      }
      const isWithinAvailableSlot = availableSlots.some(slot => {
        return slot.available &&
            startMinutes >= slot.startMinutes &&
            endMinutes <= slot.endMinutes;
      });
      if (!isWithinAvailableSlot) {
        return { valid: false, reason: `Time range is not fully within an available slot.` };
      }

      return { valid: true };
    }
  }
};
</script>
<style scoped>

.slot {
  position: relative;
  border-bottom: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
  background-color: #fefefe;
  z-index: 1;
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}
.slot.available{
  cursor: pointer;
}
.slot:hover {
  box-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12) !important;
}
slot:hover .slot-plus{
  opacity: 1;
}
.slot.unavailable {
  background-color: #bfbfbf;
  pointer-events: none;
  opacity: 1;
}
.slot.past{
  background-color: #e0f4f4;
  pointer-events: none;
  opacity:1;
}
.slot-plus {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}
.slot-plus .plus-icon{
  width: 20px;
  height: 20px;
}
.capacity-based {
  width: 100%;
  display: flex;
  justify-content: center;
}
.capacity-based .attendance-box{
  min-width: 100px;
  margin-left: 10px;
  color: teal;
}
</style>