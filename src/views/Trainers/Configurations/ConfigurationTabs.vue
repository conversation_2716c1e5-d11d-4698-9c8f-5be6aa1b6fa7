<script >

export default {
  props:{
    value:String,
  },
  computed:{
    inputValue:{
      get(){
        return this.value;
      },
      set(value){
        this.$emit('input',value);
      }
    }
  }
}
</script>

<template>
  <v-tabs
      v-model="inputValue"
      centered
      class="q-tabs-secondary shadow-0 border-bottom w-fit"
      icons-and-text
      height="48"
      light
  >
    <v-tab href="#packages">
      Packages
    </v-tab>
    <v-tab href="#levels">
      Levels
    </v-tab>
    <v-tab href="#fields">
      Fields
    </v-tab>
    <v-tab href="#documents">
      Documents
    </v-tab>
    <v-tab href="#tags">
      Quick Scan
    </v-tab>
  </v-tabs>

</template>

<style scoped>

</style>
