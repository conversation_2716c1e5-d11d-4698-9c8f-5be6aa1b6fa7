<template>
  <v-dialog v-model="modal" scrollable max-width="700px" persistent >
    <v-card >
      <v-card-text class="border-bottom">
        <div class="row pt-1 border-bottom">
          <div class="col-md-12">
            <div class="d-flex justify-space-between align-center mt-2">
              <SvgIcon class="text-2xl font-semibold" :text="`${action} Participant`" style="color: black"></SvgIcon>
              <v-btn  fab x-small class="shadow-0" @click="close">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </div>
        <div class="mt-4">
          <v-row>
            <v-col  sm="12" md="4" lg="4">
              <label for="">Session Date</label>
              <date-field
                  v-model="to_date"
                  @change="loadTimes"
                  :rules="[(v) => !!v || 'Date is required']"
                  :backFill="checkBackfillPermission($modules.schedule.management.slug)"
                  class-name="q-text-field shadow-0"
                  :hide-details="true"
                  :dense="true"
                  bg-color=""
                  label="">
              </date-field>
            </v-col>
            <v-col  sm="12" md="4" lg="4">
              <label for="">Session Start Time*</label>
              <v-select
                  class="q-autocomplete shadow-0"
                  outlined
                  hide-details
                  dense
                  :menu-props="{ bottom: true, offsetY: true }"
                  label=""
                  background-color="#fff"
                  item-text="formatted"
                  item-value="time"
                  :items="times"
                  @change="changeStartTime"
                  v-model="to_start_time"

              >
              </v-select>
            </v-col>
            <v-col  sm="12" md="4" lg="4">
              <label for="">Session End Time*</label>
              <v-select
                  class="q-autocomplete shadow-0"
                  outlined
                  hide-details
                  dense
                  :menu-props="{ bottom: true, offsetY: true }"
                  label=""
                  background-color="#fff"
                  item-text="formatted"
                  item-value="time"
                  readonly
                  :items="endTimes"
                  v-model="to_end_time"
              >
                <template v-slot:item="{ item }">
                  <span v-html="item.formatted"></span>
                </template>
                <template v-slot:selection="{ item }">
                  <span v-html="item.formatted"></span>
                </template>
              </v-select>
            </v-col>
          </v-row>
        </div>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn class="ma-2 " text @click="close">Close</v-btn>
        <v-btn
            @click="save()"
            class="ma-2 white--text teal-color"
            text
        >Confirm Action</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script >
import moment from "moment";
import SvgIcon from "@/components/Image/SvgIcon.vue";

export default {
  name: "MoveCopyParticipant",
  components: {SvgIcon},

  props:{
    modal: { type: Boolean, default: false },
    raceId: { type: Number },
    raceParticipantId: { type: Number },
    venueServiceId: { type: Number },
    facilityId: { type: Number },
    action:{
      type: String,
      default: 'copy',
    },
  },
  mounted(){
    this.loadTimes();
  },
  computed: {},
  data(){
    return {
      to_date:moment().format('YYYY-MM-DD'),
      to_start_time:null,
      to_end_time:null,
      times: [],
      endTimes:[],
      minBookingTime:null,
    }
  },
  methods: {
    changeStartTime() {
      this.endTimes = this.times;
      let expectedEndTime = moment(this.to_start_time, "HH:mm:ss").add(this.minBookingTime, "minutes").format("HH:mm:ss");
      this.endTimes = this.endTimes.filter((item) => {
        return item.time >= expectedEndTime;
      });
      this.to_end_time = null;
      if (this.endTimes.length > 0) {
        this.to_end_time = this.endTimes[0].time;
      }
    },
    loadTimes(){
      this.showLoader("Loading");
      let url ='&date='+this.to_date+'&venue_service_id='+this.venueServiceId+'&facility_id='+this.facilityId+'&race_participant_id='+this.raceParticipantId;

      this.$http.get(`venues/facilities/bookings/race/management/get-times?${url}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.times;
              if(data.length > 0) {
                this.times = data;
                this.minBookingTime = response.data.min_booking_time
              }else{
                this.showError("Race data not found");
                this.close();
              }
            }
          }).catch((error) => {
              this.errorChecker(error);
            })
          .finally(() => {
              this.hideLoader();
          })
      // this.times[index] = data.times;
    },
    close(){
      this.$emit('close');
    },
    save(){
      let data = {
        race_id: this.raceId,
        race_participant_id: this.raceParticipantId,
        to_date: this.to_date,
        to_start_time: this.to_start_time,
        to_end_time: this.to_end_time,
        venue_service_id: this.venueServiceId,
        facility_id: this.facilityId,
        action: this.action.toLowerCase()
      };
      this.showLoader("Processing...");
      this.$http.post(`venues/facilities/bookings/race/management/copy-move-participant`,data)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data;
              this.showSuccess(data.message);
              this.close();
            }
          }).catch((error) => {
            this.errorChecker(error);
          }).finally(() => {
            this.hideLoader();
          })
    }

  },

}
</script>


<style scoped>

</style>