<template>
  <v-dialog v-model="modal" scrollable width="800" @close="close" persistent>
    <v-card class="card-rounded-bottom">
      <v-card-text class="border-bottom">
        <v-form ref="form" v-model="valid" lazy-validation>
        <div class="row pt-1 border-bottom">
          <div class="col-md-12">
            <div class="d-flex justify-space-between align-center mt-2">
              <SvgIcon class="text-2xl font-semibold"
                       text="Choose Race Format" style="color: black">
              </SvgIcon>
              <v-btn fab x-small class="shadow-0" @click="close">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </div>
        <div class="mt-6">
          <p class="race-heading-text">Original Session</p>
          <v-row>
            <v-col md="4">
              <label for="">Date</label>
              <date-field
                  v-model="data.date"
                  :dayName="true"
                  :dense="true"
                  :disabled="true"
                  :hide-details="true"
                  :readonly="true"
                  background-color="#fff"
                  class-name="q-text-field shadow-0"
                  label=""
                  outlined
              >
              </date-field>
            </v-col>
            <v-col md="4">
              <label for="">Time</label>
              <v-text-field
                  v-model="data.start_time"
                  required
                  outlined
                  background-color="#fff"
                  :rules="[(v) => !!v || 'Start time is required']"
                  dense
                  :readonly="true"
                  class="q-text-field shadow-0"
                  hide-details="auto"
              ></v-text-field>
            </v-col>
            <v-col md="4">
              <label for="">Race Format</label>
              <v-select
                  v-model="race_format"
                  :items="raceFormats"
                  item-text="name"
                  item-value="id"
                  outlined
                  background-color="#fff"
                  required
                  :rules="[(v) => !!v || 'Race Format is required']"
                  class="q-autocomplete shadow-0"
                  hide-details="auto"
                  placeholder="Choose a format"
                  dense
                  @change="changeRaceFormat"
              >
              </v-select>
            </v-col>
          </v-row>
          <div v-if="selected_sessions.length">

            <v-divider class="mt-4 mb-3"/>
            <p class="race-heading-text">Subsequent session(s)</p>
          </div>
          <v-row v-for="(formats,ind) in selected_sessions" :key="ind">
            <v-col md="4">
              <label for="">Date</label>
              <date-field
                  v-model="formats.date"
                  :dayName="true"
                  :dense="true"
                  :hide-details="true"
                  background-color="#fff"
                  class-name="q-text-field shadow-0"
                  label=""
                  @change="loadTimes(ind)"
                  outlined
              >
              </date-field>
            </v-col>
            <v-col md="4">
              <label for="">Facility</label>
              <v-select
                  v-model="formats.facility_id"
                  :items="facilities"
                  item-value="id"
                  outlined
                  :menu-props="{ bottom: true, offsetY: true }"
                  background-color="#fff"
                  item-text="name"
                  label=""
                  required
                  :rules="[(v) => !!v || 'Facility is required']"
                  dense
                  hide-details="auto"
                  @change="loadTimes(ind)"
                  class="q-text-field shadow-0"
              ></v-select>
            </v-col>
            <v-col md="4">
              <label for="">Time</label>
              <v-select
                  class="q-autocomplete shadow-0"
                  outlined
                  hide-details
                  dense
                  :menu-props="{ bottom: true, offsetY: true }"
                  label=""
                  background-color="#fff"
                  item-text="formatted"
                  item-value="time"
                  :items="times[ind]"
                  v-model="formats.start_time"
                  :rules="[(v) => !!v || 'Time is required']"
              >
              </v-select>
            </v-col>
          </v-row>
          <v-row v-if="selected_sessions.length">
            <v-col md="12">
              <span style="font-size: 12px"><strong style="color: red">Note: </strong>These slots will be disabled for online booking.</span>
            </v-col>
          </v-row>
        </div>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
            class="ma-2 white--text blue-color"
            text
            @click="close"
        >Close
        </v-btn
        >
        <v-btn class="ma-2 white--text teal-color" text @click="storeRaceFormat">
          Save
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";

export default {
  name: "RaceFormatModal",
  components: {SvgIcon},
  mounted() {
    this.getFacilities()
  },
  props: {
    data: {type: Object, default: null},
    venueServiceId: {type: Number, default: null},
  },
  computed: {
    raceFormats() {
      return this.venueServiceConfiguration && this.venueServiceConfiguration.race_formats && this.venueServiceConfiguration.race_formats.length ? this.venueServiceConfiguration.race_formats : [];
    },
    venueServiceConfiguration() {
      return this.$store.getters.getConfigurationByVenueServiceId(
          this.venueServiceId
      )
    },
  },
  data() {
    return {
      modal: true,
      valid: true,
      race_format: null,
      allowed_sessions: 0,
      selected_sessions: [],
      facilities: [],
      default_selection: {
        facility_id: null,
        date: null,
        start_time: null,
      },
      times: [],
      minBookingTime: null,
      initial: true,
    }
  },
  methods: {
    async loadTimes(index) {

      if (this.initial && index > 0) {
        this.times[index] = this.times[0];
        return;
      }

      this.showLoader("Loading");
      let url = '&date=' + this.selected_sessions[index].date + '&venue_service_id=' + this.venueServiceId + '&facility_id=' + this.selected_sessions[index].facility_id;

      await this.$http.get(`venues/facilities/bookings/race/management/get-times?${url}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.times;
              if (data.length > 0) {
                this.times[index] = data;
                this.minBookingTime = response.data.min_booking_time
                this.$forceUpdate();
              } else {
                this.showError("Race data not found");
                this.close();
              }
            }
          }).catch((error) => {
            this.errorChecker(error);
          })
          .finally(() => {
            this.hideLoader();
          })
    },
    getFacilities() {
      let venueServiceId = this.venueServiceId
      this.$http
          .get(`venues/facilities/short?venue_service_id=${venueServiceId}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.facilities = response.data.data
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    async changeRaceFormat() {
      this.initial = true;
      let selectedFormat = this.raceFormats.find(item => item.id == this.race_format);
      this.selected_sessions = [];
      for (let a = 0; a < selectedFormat.allowed_sessions; a++) {
        this.selected_sessions.push({
          facility_id: this.data.facility_id,
          date: this.data.date,
          start_time: null,
        });
        await this.loadTimes(a);
      }
      this.initial = false;
      this.$forceUpdate();
    },
    storeRaceFormat() {
      if (!this.$refs.form.validate()) {
        this.showError("Please fill all required fields");
        return;
      }
      this.showLoader("Processing...");
      let dataToSend = {
        original_data:this.data,
        race_format:this.race_format,
        selected_sessions:this.selected_sessions,
      }
      this.$http
          .post(`venues/facilities/bookings/race/management/make-race-format`,dataToSend)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.facilities = response.data.data
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          }).finally(() => {
            this.hideLoader();
            this.close();
      })
    },
    close() {
      this.$emit("close");
    }
  }
}
</script>


<style scoped>
.race-heading-text {
  font-weight: 600;
  color: black;
}
</style>