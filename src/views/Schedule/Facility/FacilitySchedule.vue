<template>
  <v-container fluid>
    <v-row justify="center" no-gutters class="mt-3 pb-4 scheduleSelection">
      <div class="d-flex bordered qp-tab-nav">
        <div class="text-lg-right pr-0" style="width: 12rem !important">
          <v-select
              v-if="checkReadPermission($modules.facility.schedule.slug) || checkReadPermission($modules.schedule.management.slug)"
              class="no-right-border"
              :items="venueServices"
              v-model="venueService"
              item-value="venue_service_id"
              item-text="name"
              outlined
              :menu-props="{ bottom: true, offsetY: true }"
              return-object
              background-color="white"
              :height="46"
              hide-details
              label="Service"
              dense
              flat
              @change="initializeScheduleForVenueService(true)"
          ></v-select>
        </div>
        <ScheduleTabs  :facility-schedule-class="true" :venue_service-id="venueService.venue_service_id"  />
      </div>
      <v-spacer></v-spacer>
      <div class="text-lg-right mr-2" style="width: 12rem !important">
        <v-select
            v-if=" checkReadPermission($modules.facility.schedule.slug) && schedule_page_configuration === 'facility'"
            class="w-right-border"
            :items="selectFacilities"
            v-model="currentFacility"
            item-value="id"
            item-text="name"
            outlined
            :height="46"
            label="Facility"
            :menu-props="{ bottom: true, offsetY: true }"
            background-color="white"
            hide-details
            dense
            flat
            @change="changeCurrentFacility()"
        ></v-select>
      </div>
      <div
          class="text-lg-right text-lg-right"
          style="width: 11rem !important"
          v-if="isGameFormationEnabled"
      >
        <v-select
            v-model="gameFormationFilter"
            :items="gameFormations()"
            label="Game Formation"
            item-value="id"
            item-text="name"
            :height="46"
            multiple
            background-color="white"
            :menu-props="{ bottom: true, offsetY: true }"
            @change="getFacilitySchedule()"
            outlined
            dense
            class="w-right-border"
        >
          <template
              v-if="gameFormations().length === gameFormationFilter.length"
              v-slot:selection="{ index }"
          >
            <span v-if="index === 0">All Services</span>
          </template>
          <template v-else v-slot:selection="{ item, index }">
            <span v-if="index === 0">{{ item.name }}, </span>
            <span v-if="index === 1">{{ item.name }}, </span>
            <span v-if="index === 2" class="grey--text caption pl-1"
            >and {{ gameFormationFilter.length - 1 }} others</span
            >
          </template>
          <template v-slot:prepend-item>
            <v-list-item ripple @click="toggle">
              <v-list-item-action>
                <v-icon
                    :color="gameFormationFilter.length > 0 ? 'teal darken-4' : ''"
                >{{ icon() }}</v-icon
                >
              </v-list-item-action>
              <v-list-item-content>
                <v-list-item-title>Select All</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <v-divider class="mt-2"></v-divider>
          </template>
        </v-select>
      </div>
      <div v-if="Number(isMyLapEnabled) !== 0" class="ml-2">
        <v-btn
            style="height: 46px; border: 1px solid #dcdcdc !important"
            color="white"
            elevation="0"
            @click="getGrandPrix"
        >
          Grandprix
        </v-btn>
      </div>
      <div class="mx-2">
        <v-hover v-slot:default="{ hover }">
          <v-btn
              style="height: 46px; border: 1px solid #dcdcdc !important"
              :elevation="hover ? 2 : 0"
              :color="hover ? 'teal' : 'white'"
              @click="getFacilitySchedule"
          >
            <v-icon>mdi-refresh</v-icon>
          </v-btn>
        </v-hover>
      </div>
      <div class="mr-2">
        <v-btn
            v-if="
            facilities.length > 0 &&
            (checkExportPermission($modules.facility.schedule.slug) || checkExportPermission($modules.schedule.management.slug))
          "
            elevation="0"
            @click="reportDownload"
            style="height: 46px; border: 1px solid #dcdcdc !important"
            color="white"
            light
            tile
        >
          <v-icon>mdi-download-box-outline</v-icon>

          Report</v-btn
        >
      </div>
    </v-row>
    <v-card class="mt-2 " style="border-radius: 12px">
      <DateNavigation
          :schedule_page_configuration="schedule_page_configuration"
          :date="date"
          :currentFacilityName="currentFacilityName"
          :venueService="venueService"
          @prevDate="prevDate"
          @nextDate="nextDate"
          @gotoCalendar="gotoCalendar"
          @changeCalendarDate="changeCalendarDate"
      />
      <div class="schedule-container">
      <div class="facility-scroll-wrapper mt-6">
        <div class="facility-row-no-wrap">
          <!-- Time Column -->
          <TimeColumn
            :timeSlots="timeSlots"
            :slotHeight="slotHeight"
            :hoveredTimeMinutes="hoveredTimeMinutes"
          />
          <!-- Facility Columns -->
          <div class="facility-column" v-for="(facility,ind) in renderedFacilities" :key="`${facility.id}_${ind}`">
            <FacilityHeaderCell
                :name="getFacilityColumnName(facility,ind)"
                :facilityId="facility.id"
                :isPublic="facility.is_public"
                :isServiceEnableOnline="isEnableOnline"
                :isGameFormationEnabled="isGameFormationEnabled"
                :facilityOnlineDisabled="facility.is_facility_online_disabled"
                :formation="facility.game_formations"
                :perCapacity="facility.per_capacity"
                :perDayCapacity="facility.capacity"
                :isEnablePerDayCapacity="facility.is_enable_per_day_capacity"
                :totalCapacity="facility.capacity"
                :totalAttendees="getTotalAttendance(facility)"
                @enableDisableOnlineFacility="enableDisableOnlineFacility"
                @showTotalAttendees="showTotalAttendees(facility.id)"
            />
            <div class="facility-schedule" :style="{ height: totalHeight + 'px' }">
              <template v-for="(time, index) in getFacilityTimeSlots(facility)">
                  <FacilityColumn
                    :key="`${facility.id}_${index}`"
                    :index="`${index}`"
                    :globalTimeIncrement="globalTimeIncrement"
                    :slotHeight="slotHeight"
                    :facility="facility"
                    :time="time"
                    @openBookingForm="openBooking"
                    @hoverTime="hoveredTimeMinutes = $event"

                  />
              </template>
              <FacilityBookingCell
                :key="`fbc_${ind}`"
                :facility="facility"
                :bookings="facility.facilityScheduleMapping"
                :startTime="getFacilityStartTime(facility)"
                :end-time="endTime"
                :globalTimeIncrement="globalTimeIncrement"
                :slotHeight="slotHeight"
                @openBookingForm="openBooking"
                @openParticipantsModel="openParticipants"
              />
            </div>
          </div>
        </div>
      </div>
      </div>
    </v-card>

    <div class="mt-4">
      <div class="legend-container" title="schedule booking legend">
        <div class="legend-item">
          <div class="color-box" style="background-color: #e0f4f4;"></div>
          <span>Past</span>
        </div>
        <div class="legend-item">
          <div class="color-box" style="background-color: rgb(206, 168, 0);"></div>
          <span>Unpaid</span>
        </div>

        <div class="legend-item">
          <div class="color-box" style="background-color: rgb(0, 89, 118);"></div>
          <span>Paid</span>
        </div>

        <div class="legend-item">
          <div class="color-box" style="background-color: #E9806E;"></div>
          <span>Unpaid, need approval</span>
        </div>

        <div class="legend-item">
          <div class="color-box" style="background-color: #558C8C;"></div>
          <span>Paid, need approval</span>
        </div>

        <div class="legend-item">
          <div class="color-box" style="background-color: #961a04;"></div>
          <span>Maintenance</span>
        </div>
        <div class="legend-item">
          <div class="color-box" style="background-color: #8789C0;"></div>
          <span>Reserved in Cart</span>
        </div>
      </div>
    </div>
<!--    @close="bookingForm.showBookingForm = false; refresh = !refresh"-->
    <booking-form
        v-bind="bookingForm"
        @repeatBookingReceipt="repeatBookingReceipt"
        @close="bookingForm.showBookingForm = false;"
        @booked="completeOrder"
        @cancel="getFacilitySchedule"
        @pay="openOrderCloseBooking"
        @refresh="getFacilitySchedule"
        :perCapacity="bookingForm.per_capacity"
        :minBookingTime="bookingForm.min_booking_time"
        :increment="bookingForm.increment"
        :openingTime="bookingForm.opening_time"
        :closingTime="bookingForm.closingTime"
        @reschedule="showRescheduleMulti"
        @rescheduleMulti="showRescheduleMulti"
        @repeatRefundAndCancel="repeatRefundAndCancel"
    ></booking-form>
    <order-details
        :id="orderId"
        :ids="orderIds"
        :isSchedulePage="true"
        @close="(orderId = null), (orderIds = null)"
        @paymentDone="getFacilitySchedule"
    ></order-details>
    <RaceParticipants
        :refresh="refresh"
        :venue_service_id="venueService.venue_service_id"
        :isTotalParticipantsShow="isTotalParticipantsShow"
        v-bind="participant"
        @open-booking="openBooking"
        @open-capacity-booking="openBookingCapacity"
        @close="(participant.showRaceParticipants = false),(isTotalParticipantsShow = false), getFacilitySchedule()"
    />
    <participants-popup
        :refresh="refresh"
        :venue_service_id="venueService.venue_service_id"
        :isTotalParticipantsShow="isTotalParticipantsShow"
        v-bind="participant"
        @open-booking="openBooking"
        @open-capacity-booking="openBookingCapacity"
        @close="(participant.showParticipants = false),(isTotalParticipantsShow = false), getFacilitySchedule()"
        @pay="openOrderCloseBooking"
        @booked="completeOrder"
    />
<!--    @close="(bookingOrderId = null), getFacilitySchedule();refresh =!refresh"-->
    <booking-details
        :venueServiceId="this.venueService.venue_service_id"
        :id="bookingOrderId"
        :is_split_payment="this.is_split_payment"
        @close="(bookingOrderId = null)"
        @receipt="showReceipt"
        @repeatBookingReceipt="repeatBookingReceipt"
        @reschedule="showRescheduleMulti"
        @rescheduleMulti="showRescheduleMulti"
        @refund="showRefund"
        @cancel="deleteBookings"
        @cancelRepeatBooking="cancelRepeatBooking"
        @repeatRefundAndCancel="repeatRefundAndCancel"
        @refundSession="
        (participant.showParticipants = false), getFacilitySchedule()
      "
        @openCustomerProfile="openCustomerProfile"
    ></booking-details>

    <reschedule-booking
        :venueServiceId="this.venueService.venue_service_id"
        :id="rescheduleId"
        :currentDate="date"
        @close="rescheduleId = null"
        @refund="showRefund"
        @booked="completeOrder"
        @reload="getFacilitySchedule"
    ></reschedule-booking>
    <reschedule-multi-booking
        :venueServiceId="this.venueService.venue_service_id"
        :ids="rescheduleIds"
        :currentDate="date"
        @close="rescheduleIds = null"
        @refund="showRefund"
        @booked="completeOrder"
        @reload="getFacilitySchedule"
    ></reschedule-multi-booking>
    <RefundNew
        v-if="refundModel.invoiceId && refund_dialog"
        v-bind="refundModel"
        :refundInvoiceData="refundInvoiceData"
        :show="refund_dialog"
        :repeatRefundAmount="repeatRefundAmount"
        :repeatBookingdata="repeatBookingdata"
        @close="
        refund_dialog = false;
        repeatRefundAmount = null;
      "
        @refund="
        (refund_dialog = false), (bookingOrderId = null), completeOrder()
      "
        @reload="getFacilitySchedule"
    />
    <customer-model v-bind="userModel" @close="userModel.userID = null" />
    <confirm-model
        v-bind="confirmOEDModel"
        @confirm="confirmOnlineEnableDisable"
        @close="confirmOEDModel.id = null"
    ></confirm-model>
  </v-container>
</template>

<script>
import CustomerModel from "../../Clients/Customer/CustomerModel";
import RescheduleBooking from "./RescheduleBooking.vue";
import RescheduleMultiBooking from "./RescheduleMultiBooking.vue";
// import FacilityColumn from "@/components/Schedule/Facility/FacilityColumn";
// import HeaderCell from "@/components/Schedule/HeaderCell";
import OrderDetails from "@/components/Order/OrderDetails";
import ParticipantsPopup from "./ParticipantsPopup";
import BookingForm from "./BookingForm";
import RefundNew from "@/components/Invoice/RefundNew.vue";
import BookingDetails from "./BookingDetails";
import moment from "moment";
import ScheduleTabs from "@/components/Schedule/ScheduleTabs.vue";
import RaceParticipants from "@/views/Schedule/Facility/RaceParticipants.vue";
import DateNavigation from "@/components/Schedule/Facility/v2/DateNavigation";
import TimeColumn from "@/components/Schedule/Facility/v2/TimeColumn";
import FacilityHeaderCell from "@/components/Schedule/Facility/v2/FacilityHeaderCell";
import FacilityColumn from "@/components/Schedule/Facility/v2/FacilityColumn";
import FacilityBookingCell from "@/components/Schedule/Facility/v2/FacilityBookingCell";
import { getFacilityOpenCloseTime,facilityScheduleMapping } from "@/utils/facilityScheduleMapping.js";

export default {
  components: {
    FacilityBookingCell,
    TimeColumn,
    RaceParticipants,
    ScheduleTabs,
    CustomerModel,
    // FacilityColumn,
    // HeaderCell,
    BookingForm,
    OrderDetails,
    BookingDetails,
    RescheduleBooking,
    RefundNew,
    RescheduleMultiBooking,
    ParticipantsPopup,
    DateNavigation,
    FacilityHeaderCell,
    FacilityColumn
  },
  data() {
    return {
      increment: 60,
      globalTimeIncrement: 15,
      height: 4800,
      startTime: "00:00:00",
      endTime: "23:59:00",
      dynamicHeight: 0,
      slotHeight: 50,
      maxVisible: 2,
      timeSlots: [],

      isMyLapEnabled:false,
      showApprovals:false,
      schedule_page_configuration: "schedule",
      week_configuration: null,
      weekDays: [],
      is_split_payment: 0,
      selectFacilities: [],
      currentFacilityObj: {},
      currentFacility: null,
      currentFacilityName: "",
      userModel: { userID: null, type: "details" },
      repeatRefundAmount: null,
      repeatBookingdata: null,
      drag: false,
      facilities: [],
      bookingForm: {},
      date: moment().format("YYYY-MM-DD"),
      currentDate: null,
      venueService: {},
      gameFormationFilter: [],
      perCapacity: 0,
      isSplitPayment: 0,
      orderId: null,
      orderIds: null,
      bookingOrderId: null,
      rescheduleId: null,
      rescheduleIds: null,
      participant: {},
      attendeesPopupParticipants: {},
      minBookingTime: 60,
      scheduleHeight: 500,
      refund_dialog: false,
      refresh: false,
      isEnableOnline: 0,
      isGameFormationEnabled: 0,
      confirmOEDModel: {
        id: null,
        title: null,
        description: null,
      },
      refundModel: { invoiceId: null, type: "full", amount: 0 },
      refundInvoiceData: {},
      isTotalParticipantsShow: false,
      facilityScheduleByDate:null,
      hoveredTimeMinutes: null,
    };
  },
  mounted() {
    this.onResize();
    if (this.$store.getters.getVenueServices.status == false) {
      this.showLoader("Loading");
      this.$store.dispatch("loadVenueServices").then(() => {
        this.$nextTick(() => {
          if (this.$store.getters.getSportsService.length) {
            this.getRouteParams();
            this.initializeScheduleForVenueService(true);
          }
          this.hideLoader();
        });
      });
    } else {
      if (this.$store.getters.getSportsService.length) {
        this.getRouteParams();
        this.initializeScheduleForVenueService(true);
      }
    }
    if (this.$store.getters.getTags.status == false) {
      this.$store.dispatch("loadTags");
    }
    this.generateSlotTiming();
  },
  computed: {
    renderedFacilities() {
      return this.scheduleHeight === 'facility'
          ? Object.values(this.facilityScheduleByDate)
          : this.facilities;
    },
    venueServices() {
      return this.$store.getters.getSportsService.filter(
          (service) => service.name != "POS"
      );
    },
    venueServiceConfiguration() {
      return this.$store.getters.getConfigurationByVenueServiceId(
          this.venueService.venue_service_id
      );
    },
    minuteHeight() {
      return this.slotHeight / this.increment;
    },
    totalHeight() {
      return 1440;
    }
  },
  methods: {
    getGrandPrix() {
      if (this.venueService.venue_service_id) {
        this.$router.push({
          name: "GrandPrix",
          params: {data: btoa(this.venueService.venue_service_id)},
        });
      } else {
        this.errorChecker("Please select service");
      }
    },
    checkBookingApprovals() {
      this.$http
          .get(`venues/facilities/bookings/pending-bookings-count`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.showApprovals = response.data.data;
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    getFormattedDate(date) {
      return moment(date, "YYYY-MM-DD").format("dddd, Do");
    },
    changeCurrentFacility() {
      let facility = this.selectFacilities.find((ele) => {
        return ele.id == this.currentFacility;
      });
      if (facility) {
        this.currentFacilityName = facility.name;
        this.currentFacilityObj = facility;
        this.initializeScheduleForVenueService();
      } else {
        this.currentFacilityName = null;
        this.currentFacilityObj = {};
      }
    },
    getFacilities(check = false) {
      if (this.venueService.venue_service_id == null) {
        this.showInfo("Please select service");
        return;
      }
      //console.log("this.schedule_page_configuration",this.schedule_page_configuration);
      if(this.schedule_page_configuration !== "facility"){
        return;
      }
      this.showLoader("Loading Facilities");
      this.selectFacilities = [];
      this.$http
          .get(`venues/facilities?venue_service_id=${this.venueService.venue_service_id}`)
          .then((response) => {
            this.hideLoader();
            if (response.status === 200 && response.data.status === true && response.data.data.length) {
              this.selectFacilities = response.data.data;
              this.selectFacilities = this.selectFacilities.filter(ele => ele.is_enabled_seat_map === 0);
              if (this.selectFacilities.length > 0 && (!this.currentFacility || check)) {
                this.currentFacility = this.selectFacilities[0].id;
                let facility = this.selectFacilities.find((ele) => {
                  return ele.id == this.currentFacility;
                });
                if (facility) {
                  this.currentFacilityName = facility.name;
                  this.currentFacilityObj = facility;
                } else {
                  this.currentFacilityName = null;
                  this.currentFacilityObj = {};
                }
                this.getFacilitySchedule();
              }
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    generateWeek(startingDate) {
      const daysOfWeek = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      const result = [];

      // Convert the starting date string to a Date object
      const startDate = new Date(startingDate);

      // Generate the list of weekdays starting from the given date
      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);
        const day = daysOfWeek[currentDate.getDay()];
        result.push({
          day: day,
          date: currentDate.toISOString().split("T")[0],
        });
      }
      return result;
    },
    generateWeekDays() {
      if (this.schedule_page_configuration !== "facility") {
        return;
      }
      this.weekDays = [];
      let startDate = this.date;
      if (this.week_configuration === "current_date") {
        this.weekDays = this.generateWeek(this.date);
      } else {
        const daysOfWeek = [
          "Sunday",
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
        ];
        const result = [];
        const currentDate = new Date(startDate);

        // Calculate the Monday of the current week
        const currentDayOfWeek = currentDate.getDay(); // 0 (Sunday) to 6 (Saturday)
        const mondayOffset = currentDayOfWeek - 1; // Offset to Monday
        const mondayDate = new Date(currentDate);
        mondayDate.setDate(mondayDate.getDate() - mondayOffset); // Set to Monday of the current week

        // Generate the list of weekdays starting from Monday
        for (let i = 0; i < 7; i++) {
          result.push({
            day: daysOfWeek[i],
            date: new Date(
                mondayDate.getFullYear(),
                mondayDate.getMonth(),
                mondayDate.getDate() + i
            )
                .toISOString()
                .split("T")[0],
          });
        }
        this.weekDays = result;
      }
    },
    goToApprovals(){
      // if (this.venueService.venue_service_id) {
      this.$router.push({
        name: "BookingApprovals",
        // params: { data: btoa(this.venueService.venue_service_id) },
      });
      // } else {
      //   this.errorChecker("Please select service");
      // }
    },
    goToConfiguration() {
      if (this.venueService.venue_service_id) {
        this.$router.push({
          name: "ScheduleConfiguration",
          params: {data: btoa(this.venueService.venue_service_id)},
        });
      } else {
        this.errorChecker("Please select service");
      }
    },
    initializeScheduleForVenueService(check = false) {
      if (this.venueService.venue_service_id) {
        if (!this.$store.getters.getConfigurationStatus(this.venueService.venue_service_id)) {
          this.$store.dispatch("loadConfigurationsByVenueServiceId", this.venueService.venue_service_id)
              .then((response) => {
                if (response.status === 200) {
                  this.schedule_page_configuration = this.venueServiceConfiguration.schedule_page_configuration;
                  this.week_configuration = this.venueServiceConfiguration.week_configuration;
                  this.isMyLapEnabled = this.venueServiceConfiguration.is_my_lap_enabled.toString();
                  if (this.venueServiceConfiguration.is_golf_enabled) {
                    this.$router.push({
                      name: "GolfSchedule",
                      params: {
                        data: btoa(
                            JSON.stringify({
                              venue_service: this.venueService,
                              date: this.date,
                            })
                        ),
                      },
                    });
                  }
                  this.globalTimeIncrement = this.venueServiceConfiguration.min_time_increment;
                  console.log("globalTimeIncrement",this.globalTimeIncrement)
                }
                this.loadFacilitiesFunc(check)
              });
        } else {
          if (this.venueServiceConfiguration.is_golf_enabled) {
            this.$router.push({
              name: "GolfSchedule",
              params: {
                data: btoa(
                    JSON.stringify({
                      venue_service: this.venueService,
                      date: this.date,
                    })
                ),
              },
            });
          } else {
            this.schedule_page_configuration = this.venueServiceConfiguration.schedule_page_configuration;
            this.week_configuration = this.venueServiceConfiguration.week_configuration;
            this.isMyLapEnabled = this.venueServiceConfiguration.is_my_lap_enabled.toString();
            this.globalTimeIncrement = this.venueServiceConfiguration.time_increment;
            console.log("globalTimeIncrement",this.globalTimeIncrement)
          }
          this.loadFacilitiesFunc(check)
        }

      }
    },
    loadFacilitiesFunc(check) {
      if (this.gameFormations().length === 0) {
        this.showLoader("Loading");
        this.$store.dispatch("loadConfigurationsByVenueServiceId", this.venueService.venue_service_id)
            .then(() => {
              this.hideLoader();
              this.generateWeekDays();
              this.getFacilitySchedule();
            });
      } else {
        this.generateWeekDays();
        this.getFacilitySchedule();
      }
      this.getFacilities(check);
    },
    getRouteParams() {
      if (this.$route.params.data) {
        let data = JSON.parse(atob(this.$route.params.data));
        this.venueService = data.venue_service;
        this.date = data.date;
        if (data.order_id) {
          this.bookingOrderId = data.order_id;
        }
        if (data.facility_id) {
          this.currentFacility = data.facility_id;
          this.changeCurrentFacility();
        }
      } else {
        this.venueService = this.$store.getters.getSportsService.filter(
            (service) => service.name != "POS"
        )[0];
      }
    },
    onResize() {
      this.scheduleHeight = window.innerHeight - 350;
    },
    gameFormations() {
       if(this.schedule_page_configuration !== "facility") {
        return this.$store.getters.getGameFormationsByVenueServiceId(this.venueService.venue_service_id);
      }
      return [];
    },
    openBooking(data) {
      //console.log("data",data);
      if (data.per_capacity === 0 && (data.status === "paid" || data.status === "trainer" || data.status === "unapproved")) {
        this.bookingOrderId = data.order_id;
      } else {
        this.openBookingForm(data);
      }
    },
    openBookingCapacity(data) {
      if (data.status == "paid" || data.status == "trainer") {
        this.bookingOrderId = data.order_id;
      } else {
        this.openBookingForm(data);
      }
    },
    openBookingForm(data) {
      this.bookingForm = {
        showBookingForm: true,
        start_time: moment(data.start_time, "hh:mm a").format("HH:mm:ss"),
        end_time: moment(data.end_time, "hh:mm a").format("HH:mm:ss"),
        facility_name: data.facility_name,
        facility_id: data.facility_id,
        date: data.current_date ? data.current_date : data.date ? data.date : this.date,
        increment: data.facility_time_increment?data.facility_time_increment:this.globalTimeIncrement,
        venue_service_id: this.venueService.venue_service_id,
        service: this.venueService.name,
        id: data.id != null ? data.id : 0,
        order_id: data.order_id?data.order_id:null,
        per_capacity: data.per_capacity,
        min_booking_time: data.min_booking_time,
        opening_time: data.opening_time,
        closing_time: data.closing_time,
        is_enable_limit_product: data.is_enable_limit_product,
        max_base_product: data.max_base_product,
      };
    },
    getFacilityBasedSchedule() {
      if (!this.currentFacility) {
        return;
      }
      this.showLoader("Loading");
      const dateArray = this.weekDays.map((item) => item.date);
      let url = `venues/facilities/bookings/facility-schedule?venue_service_id=${this.venueService.venue_service_id}&date=${this.date}&facility_id=${this.currentFacility}`;
      url += '&weekdays='+dateArray.join(',');
      if (this.gameFormationFilter.length ) {
        url += this.gameFormationFilter.map((item, index) => `&game_formation_id[${index}]=${item}`).join(",");
      }
      let backfill = this.checkBackfillPermission(this.$modules.facility.schedule.slug);
      console.log("backfilkl",backfill);
      this.$http.get(url)
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              console.log("getFacilityBasedSchedule calling 0000");
              const data = response.data.data;
              this.increment = data.configuration.time_increment != null ? data.configuration.time_increment : 60;
              this.minBookingTime = data.configuration.min_booking_time != null ? data.configuration.min_booking_time : 60;
              this.globalTimeIncrement =  this.increment;
              this.perCapacity = data.configuration.per_capacity ? data.configuration.per_capacity : 0;
              this.generateSlotTiming();
              this.facilities = data.facilities;
              this.isEnableOnline = data.configuration.is_public;
              this.isGameFormationEnabled = data.configuration.is_game_formation_enabled;
              this.hideLoader();
              if (this.date !== this.currentDate) {
                this.currentDate = moment().format("YYYY-MM-DD");
              }
              console.log( "this.facilities", this.facilities);

              const scheduleByDate = data.facilities || {}; // your response structure
              this.facilityScheduleByDate = {};
              for (const [date, facility] of Object.entries(scheduleByDate)) {
                facility._date = date; // annotate
                this.processFacilitySchedule([facility],date);
                this.facilityScheduleByDate[date] = facility;
              }
            }
            this.checkBookingApprovals();
          });
    },
    getFacilitySchedule() {
      this.generateWeekDays();
      if (this.schedule_page_configuration === "facility") {
        return this.getFacilityBasedSchedule();
      }
      if (this.participant && this.participant.showParticipants) {
        this.refresh = !this.refresh;
      }
      this.showLoader("Loading");
      let url = `venues/facilities/bookings/schedule-new?venue_service_id=${this.venueService.venue_service_id}&date=${this.date}`;
      if (this.gameFormationFilter.length) {
        url += this.gameFormationFilter.map((item, index) => `&game_formation_id[${index}]=${item}`).join(",");
      }
      let backfill = this.checkBackfillPermission(this.$modules.facility.schedule.slug);
      console.log("backfill",backfill);
      this.$http.get(url).then((response) => {
        if (response && response.status === 200 && response.data.status === true) {
          //console.log("getFacilitySchedule calling");
          const data = response.data.data;
          this.increment = data.configuration.time_increment != null ? data.configuration.time_increment : 60;
          this.minBookingTime = data.configuration.min_booking_time != null ? data.configuration.min_booking_time : 60;
          this.globalTimeIncrement =  this.increment;//data.configuration.min_time_increment?data.configuration.min_time_increment:this.minBookingTime;
          this.perCapacity = data.configuration.per_capacity ? data.configuration.per_capacity : 0;

          this.generateSlotTiming();
          this.facilities = data.facilities;
          this.isEnableOnline = data.configuration.is_public;
          this.isGameFormationEnabled = data.configuration.is_game_formation_enabled;
          this.hideLoader();
          if (this.date !== this.currentDate) {
            this.currentDate = moment().format("YYYY-MM-DD");
          }
          this.processFacilitySchedule(this.facilities);
        }
      })
    },
    nextDate() {
      let amount = 1;
      if (this.schedule_page_configuration === "facility") {
        amount = 7;
      }
      this.date = moment(this.date).add(amount, "days").format("YYYY-MM-DD");
      this.generateWeekDays();
      this.getFacilitySchedule();
    },
    prevDate() {
      let amount = 1;
      if (this.schedule_page_configuration === "facility") {
        amount = 7;
      }
      this.date = moment(this.date)
          .subtract(amount, "days")
          .format("YYYY-MM-DD");
      this.generateWeekDays();
      this.getFacilitySchedule();
    },
    openOrderCloseBooking(orderId) {
      this.bookingForm.showBookingForm = false;
      this.orderId = orderId;
      // console.log("order id: " + this.orderId);
    },
    completeOrder(orderId) {
      this.getFacilitySchedule();
      if (orderId) {
        if (this.bookingForm.per_capacity) {
          this.bookingForm.showBookingForm = false;
          // console.log(this.bookingForm);
          this.openParticipants(this.bookingForm);
        } else {
          this.openOrderCloseBooking(orderId);
        }
      } else {
        this.bookingForm.showBookingForm = false;
      }
    },
    showReceipt(id) {
      this.bookingOrderId = null;
      this.orderId = id;
    },

    repeatBookingReceipt(orderIds) {
      this.bookingOrderId = null;
      this.bookingForm.showBookingForm = false;
      this.orderIds = orderIds;
    },

    showReschedule(id) {
      this.bookingOrderId = null;
      this.rescheduleId = id;
    },
    showRescheduleMulti(ids) {
      this.bookingOrderId = null;
      if (typeof ids === "number") {
        this.rescheduleIds = [ids];
        console.log("number");
      } else {
        if (ids && ids.booking_ids && Array.isArray(ids.booking_ids)) {
          this.rescheduleIds = ids.booking_ids;
        } else {
          this.rescheduleIds = [ids];
        }
      }
    },
    showRefund(id) {
      this.$store.dispatch("loadOrderDetails", id).then((response) => {
        if (response.status == 200) {
          this.refund_dialog = true;
        }
      });
      this.refund_dialog = true;
    },

    openCustomerProfile(userId) {
      this.userModel.userID = parseInt(userId);
      this.userModel.type = "details";
    },

    repeatRefundAndCancel(amount, formData) {
      if (amount) {
        this.payments = [
          {
            card_type_id: null,
            payment_code: null,
            payment_method_id: null,
            amount: null,
            payment_method: null,
          },
        ];

        this.$store.commit("setOrderPayments", this.payments);
        this.repeatRefundAmount = amount;
        this.repeatBookingdata = formData;
        this.refund_dialog = true;

        this.refundModel.invoiceId = 1;
        this.refundModel.amount = amount;
        this.refundModel.type = "partial";
      }
    },

    openParticipants(data) {
      this.is_split_payment = data.is_split_payment?1:0;
      this.isTotalParticipantsShow=false;
      this.participant = {
        showParticipants: Number(this.isMyLapEnabled) === 0,  // Explicitly check if it's 0
        showRaceParticipants: Number(this.isMyLapEnabled) !== 0,  // True for any non-zero value
        start_time: moment(data.start_time, "hh:mm a").format("HH:mm:ss"),
        end_time: moment(data.end_time, "hh:mm a").format("HH:mm:ss"),
        facility_id: data.facility_id,
        date: data.current_date
            ? data.current_date
            : data.date
                ? data.date
                : this.date,
        is_split_payment: data.is_split_payment ? data.is_split_payment : 0,
      };

    },
    gotoCalendar() {
      this.$router.push({
        name: "CalendarWithParams",
        params: {
          data: btoa(
              JSON.stringify({
                venue_service: this.venueService,
                date: this.date,
                dayUrl:"facility-schedule"
              })
          ),
        },
      });
    },
    toggle() {
      this.$nextTick(() => {
        if (this.gameFormationFilter.length === this.gameFormations().length) {
          this.gameFormationFilter = [];
        } else {
          this.gameFormationFilter = this.gameFormations().map((item) => item.id);
        }
      });
      setTimeout(() => {
        console.log("calling toggle");
        this.getFacilitySchedule();
      });
    },
    icon() {
      if (this.gameFormationFilter.length === this.gameFormations())
        return "mdi-close-box";
      if (this.gameFormationFilter.length === 0)
        return "mdi-checkbox-blank-outline";
      return "mdi-minus-box";
    },
    scrollToAvailable() {
      setTimeout(() => {
        const el = this.$refs.schedule;
        let available = this.$el.getElementsByClassName("available")[0];
        if (typeof available !== "undefined") {
          if (available.offsetTop) el.scrollTop = available.offsetTop - 20;
        }
      });
    },
    confirmCancel() {
      this.confirmModel = {
        id: this.order_id,
        title: `Do you want cancel this booking?`,
        description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
        type: "cancel",
      };
    },
    deleteBookings(id) {
      this.showLoader("Wait");
      this.$http
          .delete(`venues/orders/${id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.showSuccess("Booking cancelled successfully");
              this.getFacilitySchedule();
              this.bookingOrderId = null;
            }
          })
          .catch((error) => {
            this.hideLoader();
            this.errorChecker(error);
          });
    },

    cancelRepeatBooking() {
      this.getFacilitySchedule();
      this.bookingOrderId = null;
    },

    reportDownload() {
      let date = moment(this.date).format("YYYY-MM-DD");
      let url =
          "?venue_service_id=" +
          this.venueService.venue_service_id +
          "&date=" +
          date;
      if (!url) return;
      this.showLoader("Downloading report");
      this.$http
          .get(`venues/facilities/bookings/bookings-schedule${url}`, {
            responseType: "blob",
          })
          .then((response) => {
            this.hideLoader();
            if (response.status == 200) {
              this.downloadFile(response, "Schedule Report");
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    onScroll() {
      const refElement = this.$refs.schedule;
      if (refElement) {
        const scrollLeft = refElement.scrollLeft;
        const scrollTop = refElement.scrollTop;
        this.$refs.scheduleTimer.scrollTop = scrollTop;
        this.$refs.scheduleHeader.scrollLeft = scrollLeft;
      }
    },
    enableDisableOnlineFacility(data) {
      let toggle = "Enabled";
      if (data.is_public) {
        toggle = "Disabled";
      }
      let date = this.date;
      if (data.date) {
        date = data.date;
      }
      this.confirmOEDModel = {
        id: Math.floor(Math.random() * 100 + 1),
        title: toggle + " online booking?",
        description:
            "By clicking <b>Yes</b> online booking for date " +
            date +
            " <b>" +
            toggle +
            "</b>.  Do you need to continue your action ?",
        type: "update",
        data: data,
      };
    },
    /** only disable for particular date */
    confirmOnlineEnableDisable(data) {
      let formData = new FormData();
      let date = moment(this.date).format("YYYY-MM-DD");
      if (data.data && data.data.date) {
        date = moment(data.data.date).format("YYYY-MM-DD");
      }
      if (
          date != "" &&
          typeof data.data.facility_id != "undefined" &&
          data.data.facility_id != "" &&
          typeof data.data.is_public != "undefined"
      ) {
        formData.append("date", date);
        formData.append("facility_id", data.data.facility_id);
        formData.append("is_public", data.data.is_public);
        this.showLoader("LOADING ... ");
        this.$http
            .post(`venues/facilities/online-status`, formData, {
              headers: {
                "Content-Type": "multipart/form-data; boundary=${form._boundary}",
              },
            })
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                this.hideLoader();
                this.getFacilitySchedule();
                this.showSuccess("Online facility booking status updated");
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            });
      }
    },
    showTotalAttendees(facility_id) {
      this.isTotalParticipantsShow = true;
      // this.attendeesPopupParticipants = {
      //   showParticipants: true,
      //   facility_id: facility_id,
      //   date: this.date,
      // };
      this.participant = {
        showParticipants: true,
        facility_id: facility_id,
        date: this.date,
      }
      console.log("show total attendace");
    },


    // New functions
    getFacilityColumnName(facility,index){
      if(this.schedule_page_configuration === 'facility'){
        return moment(index, "YYYY-MM-DD").format("dddd, Do");
      }else{
        return facility.name;
      }
    },
    getFacilityStartTime(facility){
      if(facility.facilityScheduleSlots && facility.facilityScheduleSlots[0] && facility.facilityScheduleSlots[0].start_time){
        return facility.facilityScheduleSlots[0].start_time;
      }else{
        return "00:00:00";
      }
    },
    getTotalAttendance(facility){
      if(facility && facility.per_capacity) {
        return facility.bookings.reduce((sum, b) => sum + (b.attendance ?? 0), 0);
      }
      return 0;
    },
    changeCalendarDate(calendarDate){
      console.log("calendarDate",calendarDate)
      this.date = calendarDate;
      this.getFacilitySchedule();
    },
    toMinutes(timeStr) {
      const [hh, mm] = timeStr.split(":").map(Number);
      return hh * 60 + mm; // discard seconds (not needed for minute slotting)
    },
    fromMinutes(min) {
      const h = String(Math.floor(min / 60)).padStart(2, '0');
      const m = String(min % 60).padStart(2, '0');
      return `${h}:${m}`;
    },
    generateSlotTiming() {
      this.timeSlots = [];
      const totalDayMinutes = 1440;
      const startTimeMoment = moment(this.startTime, "HH:mm:ss");
      const endTimeMoment = moment(this.endTime, "HH:mm:ss");
      const data = [];
      for (let index = 0; index < totalDayMinutes; index += this.globalTimeIncrement) {
        let start = moment().startOf("day").add(index, "minutes");
        let end = start.clone().add(this.globalTimeIncrement, "minutes");

        // Fix potential wrap at midnight
        if (end.format("HH:mm:ss") === "00:00:00") {
          end = start.clone().add(this.globalTimeIncrement - 1, "minutes");
        }
        if (start.isSameOrAfter(startTimeMoment) && end.isSameOrBefore(endTimeMoment)) {
          data.push({
            id: index,
            start_time: start.format("HH:mm:ss"),
            end_time: end.format("HH:mm:ss"),
            formated_start_time: start.format("hh:mm a"),
            formated_end_time: end.format("hh:mm a"),
            startMinutes: index,
            endMinutes: index + this.globalTimeIncrement,
            height: 60, // this will be global slot height
            fixed: true
          });
        }
      }
      this.timeSlots = data;
    },
    generateTimeSlotsForFacility(facility,fDate = null) {
      const date = fDate?fDate:this.date;
      const backFill = this.checkBackfillPermission(
          this.$modules.facility.schedule.slug
      ) || this.checkBackfillPermission(
          this.$modules.schedule.management.slug
      );

      const slots = [];
      const rentals = facility.facility_rentals || [];
      const increment = facility.facility_time_increment > 0 ? facility.facility_time_increment : 60;
      const minSlotSize = facility.min_booking_time || 15; // define minimum acceptable slot if you want to handle partials

      const now = moment();
      const isToday = date === now.format('YYYY-MM-DD');
      const dayEnd = 1440;

      const rentalRanges = rentals.map(r => {
        const end = r.end_time === '23:59:59' ? 1440 : this.toMinutes(r.end_time);
        return {
          start: this.toMinutes(r.start_time),
          end: Math.min(end, dayEnd)
        };
      }).sort((a, b) => a.start - b.start);

      let cursor = 0;
      while (cursor < dayEnd) {
        // Find rental covering this time
        const rental = rentalRanges.find(r => cursor >= r.start && cursor < r.end);

        if (rental) {
          let rentalCursor = cursor;
          const rentalEnd = rental.end;
          // Generate full increment slots
          while (rentalCursor + increment <= rentalEnd) {
            const next = rentalCursor + increment;
            const isPast = !backFill && isToday && moment(`${this.date} ${this.fromMinutes(rentalCursor, 'HH:mm')}`, 'YYYY-MM-DD HH:mm').isBefore(now);
            const endTime = this.fromMinutes(next);
            slots.push({
              startMinutes: rentalCursor,
              endMinutes: next,
              start_time: this.fromMinutes(rentalCursor, 'HH:mm'),
              end_time: endTime === "24:00"?"23:59":endTime,
              formated_start_time: this.fromMinutes(rentalCursor),
              formated_end_time: this.fromMinutes(next),
              available: true,
              isPast,
              status: isPast ? 'past' : 'available',
            });
            rentalCursor = next;
          }

          // Handle leftover (partial) slot
          if (rentalCursor < rentalEnd) {
            const next = rentalEnd;
            const isPast = !backFill && isToday && moment(`${this.date} ${this.fromMinutes(rentalCursor, 'HH:mm')}`, 'YYYY-MM-DD HH:mm').isBefore(now);

            const duration = next - rentalCursor;
            const partialAvailable = duration >= minSlotSize;
            slots.push({
              startMinutes: rentalCursor,
              endMinutes: next,
              start_time: this.fromMinutes(rentalCursor, 'HH:mm'),
              end_time: this.fromMinutes(next, 'HH:mm'),
              formated_start_time: this.fromMinutes(rentalCursor),
              formated_end_time: this.fromMinutes(next),
              available: partialAvailable,
              isPast,
              status: partialAvailable ? (isPast ? 'partial_past' : 'partial') : 'unavailable',
            });
            rentalCursor = next;
          }
          cursor = rentalEnd;
        } else {
          // Unavailable block until next rental or day end
          const next = rentalRanges.find(r => r.start > cursor)?.start || dayEnd;
          const isPast = !backFill && isToday && moment(`${this.date} ${this.fromMinutes(cursor, 'HH:mm')}`, 'YYYY-MM-DD HH:mm').isBefore(now);
          slots.push({
            startMinutes: cursor,
            endMinutes: next,
            start_time: this.fromMinutes(cursor, 'HH:mm'),
            end_time: this.fromMinutes(next, 'HH:mm'),
            formated_start_time: this.fromMinutes(cursor),
            formated_end_time: this.fromMinutes(next),
            available: false,
            isPast,
            status: 'unavailable',
          });
          cursor = next;
        }
      }
      return slots;
    },
    getFacilityTimeSlots(facility) {
      return facility.facilityScheduleSlots || this.generateTimeSlotsForFacility(facility);
    },
    processFacilitySchedule(facilitiesArray,fDate = null) {
      facilitiesArray.forEach((facility) => {
        facility.facility_rentals = Array.isArray(facility.facility_rentals) ? facility.facility_rentals : [];
        //facility._availableMinutes = generateAvailableMinutes(facility.facility_rentals, this.toMinutes);
        facility.facilityScheduleSlots = this.generateTimeSlotsForFacility(facility,fDate?fDate:this.date);
        facility.facilityScheduleMapping = facilityScheduleMapping(facility, fDate?fDate:this.date);
        console.log("facility.facilityScheduleMapping");
        console.log(facility.facilityScheduleMapping);
        //console.log(" facility.facilityScheduleMapping", facility.facilityScheduleMapping);
        const { opening_time, closing_time } = getFacilityOpenCloseTime(facility.facility_rentals);
        facility.opening_timem = opening_time;
        facility.closing_time = closing_time;
      });
    }
  },
};
</script>

<style lang="scss">
.no-scroll {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.no-scroll::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

.sch-gameformation-field {
  height: 44px;
}

.no-right-border {
  .v-input__control {
    border-radius: 5px 0 0 5px; /* Adjust border radius as needed */
  }
  color: #4faeaf !important;
  fieldset {
    border: 1px solid rgba(17, 42, 70, 0.1);
  }
}

.w-right-border {
  .v-input__control {
    border-radius: 5px; /* Adjust border radius as needed */
  }

  color: #4faeaf !important;

  fieldset {
    border: 1px solid rgba(17, 42, 70, 0.1);
  }
}

.no-border-radius{
  border-radius: 0 !important;
}
.no-left-border {
  border-radius: 0 5px 5px 0 !important;
  border: 1px solid rgba(17, 42, 70, 0.1);
  border-left: none;
}

.legend-container {
  display: flex;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 12px;
}

.color-box {
  width: 14px;
  height: 14px;
  margin-right: 8px;
  border-radius: 2px;
  border: 1px solid black;
}
.schedule-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px); /* Adjust 100px if DateNavigation takes more or less space */
  overflow: hidden;
}
.schedule-container > .v-card__text:first-child {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white; /* Or your theme */
}
.facility-scroll-wrapper {
  flex: 1;
  overflow: auto; /* Enables both horizontal and vertical scroll if needed */
}
.facility-row-no-wrap {
  display: flex;
  flex-wrap: nowrap;
  min-width: max-content;
}
.facility-column {
  flex: 1;
  min-width: 200px;
  max-width: 200px;
  flex-shrink: 0;
  position: relative;
  display: flex;
  flex-direction: column;
}
.facility-header {
  text-align: center;
  border-bottom: 1px solid #ccc;
  padding: 8px 4px;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 3;
  background-color: rgb(233, 241, 246);
  color: black;
  font-weight: 600;
  min-height: 64px;
}
.facility-header span{
  display: block;
}
.facility-schedule {
  position: relative;
  background: #ffffff;
}
.time-label {
  font-size: 14px;
  border-bottom: 1px solid;
  border-right: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
  line-height: 50px;
  text-align: center;
  min-width: 180px;
}
.booking {
  position: absolute;
  cursor: pointer;
}
.facility-online-badge{
  position: absolute;
  top: 5px;
  right:0;
}




</style>
