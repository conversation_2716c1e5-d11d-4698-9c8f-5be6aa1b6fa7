import moment from "moment/moment";
export function facilityScheduleMapping(facility,date = null) {
    const fb = Array.isArray(facility.bookings) ? facility.bookings : [];
    const normalized = [];
    const isCapacityBased = !!facility.per_capacity;
    const frTime = getFacilityRentalsStartEndTime(facility);
    if (isCapacityBased) {
        const increment = facility.facility_time_increment || 30;
        const slotMap = new Map();

        fb.forEach((b) => {
            const bookingStart = moment(b.start_time, "HH:mm:ss");
            const bookingEnd = moment(b.end_time, "HH:mm:ss");

            let current = bookingStart.clone();

            while (current.isBefore(bookingEnd)) {
                const slotStart = current.clone();
                const slotEnd = moment.min(slotStart.clone().add(increment, 'minutes'), bookingEnd);
                const slotKey = `${slotStart.format("HH:mm:ss")}-${slotEnd.format("HH:mm:ss")}`;

                if (!slotMap.has(slotKey)) {
                    slotMap.set(slotKey, {
                        start_time: slotStart.format("HH:mm:ss"),
                        end_time: slotEnd.format("HH:mm:ss"),
                        date: b.date,
                        status: getStatus(b),
                        isPast: isBookingPast(b),
                        isCapacityBased: true,
                        capacity: facility.capacity,
                        attendance_count: 0,
                        name: '',
                        bookings: [],
                        isShowPlus: slotEnd.diff(slotStart, 'minutes') >= increment
                    });
                }

                const slot = slotMap.get(slotKey);
                slot.attendance_count += b.attendance ?? 1;
                slot.bookings.push(b);
                slot.name = slot.name || b.name;
                slot.id = b.id;
                slot.is_trainer = b.is_trainer;
                slot.order_notes = b.order_notes;
                slot.order_id = b.order_id;
                slot.is_open_dated = b.is_open_dated;
                slot.is_split_payment = b.is_split_payment;
                slot.customer_id = b.customer_id;

                // Advance by increment
                current = slotEnd.clone(); // ✅ instead of add(increment) blindly
            }
        });
        normalized.push(...Array.from(slotMap.values()));
    } else {
        // check overnight booking
        fb.forEach((b) => {
            let isOverNight = false;
            let endTime =  b.end_time;
            let startTime = b.start_time;
            if(date === b.date && b.booking_end_date && (date !== b.booking_end_date)){
                endTime = b.booking_end_date !== b.date?frTime.fr_end_time:b.end_time;
                console.log("endTime overnight",endTime);
            }else if(b.booking_end_date && date === b.booking_end_date && (date !== b.date)){
                startTime = b.booking_end_date !== b.date?frTime.fr_start_time:b.start_time;
                endTime = b.end_time;
                isOverNight = true;
                console.log("endTime overnight else if")
            }else{
                //console.log("date",date);
                //console.log("b.booking_end_date",b.booking_end_date);
            }
            normalized.push({
                start_time: startTime,
                end_time: endTime,
                date: b.date,
                status: getStatus(b),
                isCapacityBased: false,
                capacity: null,
                attendance_count: 1,
                name:b.name,
                id: b.id, // facility booking id
                is_trainer:b.is_trainer,
                order_notes:b.order_notes,
                order_id: b.order_id,
                is_open_dated: b.is_open_dated,
                is_split_payment:b.is_split_payment,
                customer_id: b.customer_id,
                facility_booking_repeat_id: b.facility_booking_repeat_id,
                is_over_night:isOverNight,
                bookings: []
            });
        });
    }
    // === Parent Dependency ===
    if(facility.parent_facilities){
        facility.parent_facilities.forEach((element) => {
            const pdfb = Array.isArray(element.bookings) ? element.bookings : [];
            pdfb.forEach((b) => {
                normalized.push({
                    start_time: b.start_time,
                    end_time: b.end_time,
                    date: b.date,
                    status: getStatus(b,'dependency'),
                    isCapacityBased: false,
                    capacity: null,
                    attendance_count: 1,
                    isDependent: true,
                    name:b.name,
                    id: b.id, // facility booking id
                    is_trainer:b.is_trainer,
                    order_notes:b.order_notes,
                    order_id: b.order_id,
                    is_open_dated: b.is_open_dated,
                    is_split_payment:b.is_split_payment,
                    customer_id: b.customer_id,
                    facility_booking_repeat_id: b.facility_booking_repeat_id,
                    bookings: []
                });
            });
        })
    }
    // === Child Dependency ===
    if(facility.child_facilities){
        facility.child_facilities.forEach((element) => {
            const cdfb = Array.isArray(element.bookings) ? element.bookings : [];
            cdfb.forEach((b) => {
                normalized.push({
                    start_time: b.start_time,
                    end_time: b.end_time,
                    date: b.date,
                    status: getStatus(b,'dependency'),
                    isCapacityBased: false,
                    capacity: null,
                    attendance_count: b.attendance,
                    isDependent: true,
                    name:b.name,
                    id: b.id, // facility booking id
                    is_trainer:b.is_trainer,
                    order_notes:b.order_notes,
                    order_id: b.order_id,
                    is_open_dated: b.is_open_dated,
                    is_split_payment:b.is_split_payment,
                    customer_id: b.customer_id,
                    facility_booking_repeat_id: b.facility_booking_repeat_id,
                    bookings: []
                });
            });
        })
    }
    // === Workshop Program Schedules ===
    if(facility.workshop_program_schedules){
        facility.workshop_program_schedules.forEach((w) => {
            normalized.push({
                start_time: w.start_time,
                end_time: w.end_time,
                date: w.date || facility.current_date || '', // if date is available
                status: 'workshop',
                isCapacityBased: false,
                isWorkshop: true,
                name: w.name,
                attendance_count: 0,
                bookings: [],
            });
        });
    }

    // === Workshop Program ReSchedules ===
    if(facility.workshop_program_reschedules){
        facility.workshop_program_reschedules.forEach((w) => {
            normalized.push({
                start_time: w.start_time,
                end_time: w.end_time,
                date: w.date || facility.current_date || '', // if date is available
                status: 'workshop',
                isCapacityBased: false,
                isWorkshop: true,
                name: w.name,
                attendance_count: 0,
                bookings: [],
            });
        });
    }
    // === Event Schedules ===
    if(facility.event_schedules){
        facility.event_schedules.forEach((e) => {
            const startDate = e.start_date;
            const endDate = e.end_date;
            const startTime = e.start_time;
            const endTime = e.end_time;
            const eventName = e.event ? e.event.name : e.schedule_name;
            const orderNotes = e.event?.internal_notes || '';
            const orderNoteClass = e.event?.internal_notes ? 'order-notes' : '';
            const currentDate = facility.current_date || date; // the currently viewed date

            const isSameDay = startDate === endDate;
            const isCurrentStartDate = startDate === currentDate;
            const isCurrentEndDate = endDate === currentDate;
            if (isSameDay && isCurrentStartDate) {
                // Normal event on same day
                normalized.push({
                    start_time: startTime,
                    end_time: endTime,
                    date: currentDate,
                    status: 'event ' + orderNoteClass,
                    isCapacityBased: false,
                    isEvent: true,
                    name: eventName,
                    attendance_count: 0,
                    order_notes: orderNotes,
                    bookings: [],
                });
            }else if (!isSameDay && isCurrentStartDate) {
                // Day 1 of a multi-day event
                normalized.push({
                    start_time: startTime,
                    end_time: "23:59",
                    date: currentDate,
                    status: 'event ' + orderNoteClass,
                    isCapacityBased: false,
                    isEvent: true,
                    name: eventName,
                    attendance_count: 0,
                    order_notes: orderNotes,
                    bookings: [],
                });
            }else if (!isSameDay && isCurrentEndDate) {
                // Last day of a multi-day event
                normalized.push({
                    start_time: "00:00",
                    end_time: endTime,
                    date: currentDate,
                    status: 'event ' + orderNoteClass,
                    isCapacityBased: false,
                    isEvent: true,
                    name: eventName,
                    attendance_count: 0,
                    order_notes: orderNotes,
                    bookings: [],
                });
            }
        });
    }

    // === Maintenance Schdeule ===
    if(facility.maintenances){
        facility.maintenances.forEach((e) => {
            normalized.push({
                start_time: e.start_time,
                end_time: e.end_time,
                date: e.date || facility.current_date || '', // if date is available
                status: 'maintenance ',
                isCapacityBased: false,
                isMaintenances: true,
                name: e.name,
                attendance_count: 0,
                bookings: [],
            });
        });
    }
    return normalized;
}
export function createBookingHash(facility, date) {
    return `${facility.id}_${facility.bookings?.length || 0}_${date}`;
}
function getStatus(b, extraClassName = "") {
    const orderNoteClass = b.order_notes ? 'order-notes' : '';
    const bookingApprovalClass = b.is_booking_approved === 0 ? 'approval' : '';
    let status = `unpaid ${orderNoteClass} ${bookingApprovalClass} ${extraClassName}`.trim();
    if (b.status_id === 1) {
        let credit = b.credit_owed?'credit':'';
        status = `paid ${orderNoteClass} ${bookingApprovalClass} ${credit} ${extraClassName}`.trim();
    }else if(b.status_id === 14){
        status = `trainer ${bookingApprovalClass} ${extraClassName}`.trim();
    }
    return status;
}
function isBookingPast(b){
    const slotTime = moment(`${b.date} ${b.start_time}`, 'YYYY-MM-DD HH:mm:ss');
    return b.date === moment().format('YYYY-MM-DD') && slotTime.isBefore(moment()) ? true : false;
}
export function getFacilityOpenCloseTime(facilityRentals) {
    if (!Array.isArray(facilityRentals) || facilityRentals.length === 0) {
        return { opening_time: null, closing_time: null };
    }
    let opening_time = facilityRentals[0].start_time;
    let closing_time = facilityRentals[0].end_time;
    for (const rental of facilityRentals) {
        if (rental.start_time < opening_time) {
            opening_time = rental.start_time;
        }
        if (rental.end_time > closing_time) {
            closing_time = rental.end_time;
        }
    }
    return { opening_time, closing_time };
}
function getFacilityStartTime(facilityRentals){
    const firstObject = facilityRentals[0];
    return firstObject?firstObject.start_time:'00:00';
}
function getFacilityEndTime(facilityRentals){
    const lastObject = facilityRentals[facilityRentals.length - 1];
    return lastObject?lastObject.end_time:'11:59';
}
function getFacilityRentalsStartEndTime(facility) {
    let data = {};
    if (facility.facility_rentals && facility.facility_rentals.length > 0) {
        let frStartTime = getFacilityStartTime(facility.facility_rentals);
        let frEndTime = getFacilityEndTime(facility.facility_rentals);
        data = {
            'fr_start_time': frStartTime,
            'fr_end_time': frEndTime
        };
    }else{
        data = {
            'fr_start_time': "00:00:00",
            'fr_end_time': "23:59:59"
        };
    }
    return data;
}