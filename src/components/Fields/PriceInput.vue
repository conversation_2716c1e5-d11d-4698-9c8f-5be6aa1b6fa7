<template>
  <v-text-field
      v-bind="$attrs"
      v-model="inputValue"
      v-on="$listeners"
      @blur="formatToFourDecimals"
      class="q-text-field shadow-0"
  />

</template>

<script>
export default {
  name: 'PriceInput',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
  },
  watch:{
    value: function (val, oldVal) {
      console.log(val,oldVal)
      this.inputValue = val;
    }
  },
  computed: {
    inputValue: {
      get() {
        const val = this.value;

        if (val === null || val === undefined || val === '') {
          return 0;
        }

        const num = parseFloat(val);
        return isNaN(num) ? 0 : parseFloat(num.toFixed(4));
      },
      set(val) {
        if (val === null || val === undefined || val === '') {
          this.$emit('input', 0);
          return;
        }

        const num = parseFloat(val);
        const formatted =  isNaN(num) ? 0 : parseFloat(num.toFixed(4));
        this.$emit('input', formatted);
      }
    }
  },
  methods: {
    formatToFourDecimals () {
      const num = parseFloat(this.inputValue);
      const formatted = isNaN(num) ? 0 : parseFloat(num.toFixed(4));
      this.$emit('input', formatted);
    }
  }
}
</script>
