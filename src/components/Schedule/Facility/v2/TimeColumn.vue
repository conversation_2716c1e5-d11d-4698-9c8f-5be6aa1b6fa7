<template>
  <div class="time-column">
    <div class="facility-header">Booking Slots</div>
    <div
        class="time-label"
        v-for="(time,index) in timeSlots"
        :key="index"
        :style="{ height: slotHeight + 'px' }"
        :class="{ 'highlight': time.startMinutes === hoveredTimeMinutes }"
    >
      {{ time.formated_start_time }} - {{ time.formated_end_time }}
    </div>
  </div>
</template>
<script>
export default {
  name: 'TimeColumn',
  props: {
    timeSlots:{
      type: Array,
      default: () => { return [] },
    },
    slotHeight: {
      type: Number,
      default: null,
    },
    hoveredTimeMinutes:{
      type: Number,
      default: null,
    },
  },
};
</script>
<style scoped>
.time-column {
  min-width: 200px;
  max-width: 200px;
}
.facility-header {
  text-align: center;
  border-bottom: 1px solid #ccc;
  padding: 8px 4px;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 3;
  background-color: rgb(233, 241, 246);
  color: black;
  font-weight: 600;
  min-height: 64px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.time-label {
  font-size: 14px;
  border-bottom: 1px solid;
  line-height: 50px;
  text-align: center;
  min-width: 180px;
  border-color: rgba(0, 0, 0, 0.12);
}
.time-label.highlight {
  background-color: #009688 !important;
  font-weight: bold;
  color: #fff !important;
}
</style>