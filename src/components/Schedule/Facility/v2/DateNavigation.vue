<template>
  <v-row>
    <v-col md="4">
      <h3 class="pt-2 pl-5" v-if="schedule_page_configuration === 'facility'">
        {{ currentFacilityName }}
      </h3>
    </v-col>
    <v-col sm="4">
      <v-row no-gutters>
        <v-col sm="1" class="text-lg-center mr-1">
          <v-btn
              :disabled="!venueService.venue_service_id"
              fab
              x-small
              color="white"
              @click="$emit('prevDate')"
          >
            <v-icon dark>mdi-menu-left</v-icon>
          </v-btn>
        </v-col>
        <v-col sm="8" class="text-lg-center">
          <date-field
              v-model="calendarDate"
              :buttonAndText="true"
              :dayName="true"
              :back-fill="checkBackfillPermission($modules.facility.schedule.slug)"
              :disabled="!venueService.venue_service_id"
              @change="changeCalendar"
          >
          </date-field>
        </v-col>
        <v-col sm="1" class="text-lg-center ml-1">
          <v-btn
              fab
              color="white"
              x-small
              :disabled="!venueService.venue_service_id"
              @click="$emit('nextDate')"
          >
            <v-icon dark>mdi-menu-right</v-icon>
          </v-btn>
        </v-col>
      </v-row>
    </v-col>
    <v-spacer></v-spacer>
    <v-col sm="4" style="text-align: right">
      <router-link :to="`/facility-schedule`">
        <v-btn color="#062b48" dark tile text style="font-weight: 600; font-size: 16px; line-height: 20px">Day</v-btn>
      </router-link>
      |
      <v-btn @click="$emit('gotoCalendar')" light tile text style="font-weight: 400; font-size: 16px; line-height: 20px">Month</v-btn>
    </v-col>
  </v-row>
</template>

<script>
import moment from "moment";
export default {
  name: 'DateNavigation',
  props: {
    date:{
      type: String,
      default: null,
    },
    schedule_page_configuration: {
      type: String,
      default: null,
    },
    currentFacilityName: {
      type: String,
      default: null,
    },
    venueService: {
      type: Object,
      default: null,
    },
  },
  data(){
    return {
      calendarDate: moment().format('YYYY-MM-DD'),
    }
  },
  mounted(){
    console.log("mounted calling")
    this.calendarDate = this.date;
  },
  watch: {
    date: function(val){
      console.log(val);
      if(this.calendarDate !== val){
        this.calendarDate = val;
      }
    }
  },
  methods: {
    changeCalendar(){
      this.$emit('changeCalendarDate', this.calendarDate);
    }
  }
};
</script>