<template>
  <div v-if="bookings">
    <div
        v-for="(sch, idx) in bookings"
        :key="`fbc_${facility.id}_${idx}`"
        :class="`booking ${sch.status}`"
        :style="getBookingStyle(facility, sch)"
        @click.stop="onClickSlot(facility, sch)"
        :title="sch.order_notes"
    >
      <div v-if="sch.isCapacityBased" class="capacity-based">
        <v-btn color="teal" fab x-small dark v-if="sch.attendance_count < sch.capacity && !sch.isPast && sch.isShowPlus" class="plus-icon">
          <v-icon small>mdi-plus</v-icon>
        </v-btn>
        <v-btn x-small dark outlined class="attendance-box"   @click.stop="getParticipants(facility, sch)">
          {{ sch.attendance_count }} <span v-if="!facility.is_enable_per_day_capacity">/ {{ sch.capacity }} </span>
        </v-btn>
      </div>
      <div v-else>
        <span v-if="sch.isWorkshop" style="display: block">ACADEMY BOOKED</span>
        <span v-else-if="sch.isEvent" style="display: block">EVENT BOOKED</span>
        <span v-else-if="sch.facility_booking_repeat_id">
           <v-icon color="white"> mdi-repeat-variant</v-icon>
        </span>
        <span v-else-if="sch.isDependent" style="display: block">DEPENDENT BOOKED</span>
        <span v-else-if="sch.is_over_night" style="display: block">(EXTENDED)</span>
        {{ sch.name }}
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'FacilityBookingCell',
  props: {
    facility:{type: Object, default: null},
    startTime: { type: String, default: "00:00:00" },
    endTime: { type: String, default: "23:59:00" },
    globalTimeIncrement:{ type: Number, default: 60 },
    slotHeight: { type: Number, default: 50 },
    isCapacityBased: { type: Boolean, default: false },
    reservedQuantity:{ type: Number, default: 0 },
    bookings: { type: Array, default: () => [] }
  },
  methods: {
    toMinutes(timeStr) {
      const [hh, mm] = timeStr.split(":").map(Number);
      return hh * 60 + mm; // discard seconds (not needed for minute slotting)
    },
    getBookingStyle(facility,sch) {
      //console.log("sch",sch);
      const startMinutes = this.toMinutes(sch.start_time);
      const endMinutes = this.toMinutes(sch.end_time);
      const durationMinutes = endMinutes - startMinutes;
      // 👇 Calculate how far from start time, in global units (to align with the full grid)
      //const top = (startMinutes - this.toMinutes(this.startTime)) / this.globalTimeIncrement * this.slotHeight;
      const firstSlot = facility.facilityScheduleSlots?.[0];
      if (!firstSlot) {
        return {
          display: 'none'
        };
      }
      // Align top position relative to the first slot's startMinutes
      const top = (startMinutes - firstSlot.startMinutes) / this.globalTimeIncrement * this.slotHeight;

      // Height in px according to duration vs time increment
      const timeIncrement = facility.facility_time_increment > 0 ? facility.facility_time_increment : facility.min_booking_time;
      const height = (durationMinutes / timeIncrement) * this.getSlotHeightForFacility(facility);
      return {
        position: "absolute",
        top: `${top}px`,
        height: `${height}px`,
        width: '100%',
        left: '0',
        zIndex: 2
      };
    },
    // getSlotHeightForFacility(facility) {
    //   const timeIncrement = facility.facility_time_increment?facility.facility_time_increment:facility.min_booking_time;
    //   return (timeIncrement / this.globalTimeIncrement) * this.slotHeight;
    // },
    getSlotHeightForFacility(facility) {
      const increment = facility.facility_time_increment || facility.min_booking_time;
      return (increment / this.globalTimeIncrement) * this.slotHeight;
    },
    onClickSlot(facility, schedule) {
      console.log("facility",facility);
      console.log("schedule",schedule);
      if (!this.checkWritePermission(this.$modules.schedule.management.slug)){
        console.log("returninnnn");
        return;
      }
      if (schedule.status === 'cart' ||
          schedule.status === "not_available" ||
          schedule.status === "maintenance" ||
          schedule.status === "event" ||
          schedule.status === "completed" ||
          schedule.status === "dependency" ||
          schedule.status === "workshop"
      ){
        console.log("return",schedule.status);
        return;
      }
      if(schedule.is_over_night){
        this.showInfo("This is extended booking, If you want to view, Please click on original booking");
        return;
      }
      let slotData = {...schedule};
      if (parseInt(this.facility.per_capacity) === 1 || parseInt(schedule.is_split_payment) === 1) {
        delete slotData.order_id;
        delete slotData.id;
      }
      slotData.capacity = facility.capacity;
      slotData.facility_name = this.facility.name;
      slotData.facility_id = parseInt(this.facility.id);
      slotData.per_capacity = parseInt(this.facility.per_capacity);
      slotData.min_booking_time = parseInt(this.facility.min_booking_time);
      slotData.facility_time_increment = parseInt(this.facility.facility_time_increment);
      slotData.opening_time = this.facility.opening_time;
      slotData.closing_time = this.facility.closing_time;
      slotData.is_enable_limit_product = this.facility.is_enable_limit_product?this.facility.is_enable_limit_product:0;
      slotData.max_base_product = this.facility.max_base_product?this.facility.max_base_product:null;
     // console.log("slotData",slotData);
      console.log("onClick slot call");
      this.$emit("openBookingForm", slotData);
    },
    getParticipants(facility, schedule) {
      console.log("get participant call");
      let slotData = {...schedule};
      slotData.capacity = facility.capacity;
      slotData.facility_name = this.facility.name;
      slotData.facility_id = parseInt(this.facility.id);
      slotData.per_capacity = parseInt(this.facility.per_capacity);
      slotData.min_booking_time = parseInt(this.facility.min_booking_time);
      slotData.facility_time_increment = parseInt(this.facility.facility_time_increment);
      slotData.opening_time = this.facility.opening_time;
      slotData.closing_time = this.facility.closing_time;
      console.log("slotData",slotData);
      this.$emit("openParticipantsModel", slotData);
    },
  }
};
</script>
<style scoped>
.booking {
  position: absolute;
  cursor: pointer;
  width: 100%;
  color:#fff;
  left:0;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
  box-sizing: border-box;
  z-index: 2;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}
.booking button{
  color: #fff !important;
}
.unpaid{
  background-color: #cea800 !important;
}
.paid{
  background-color: #005976 !important;
}
.booking.unpaid.approval {
  background-color: rgb(233, 128, 110) !important;
}
.booking.paid.approval {
  background-color: rgb(85, 140, 140) !important;
}
.booking.workshop,.booking.event {
  background: #91a0b4 !important;
  cursor: not-allowed !important;
  border: 0 solid #e0f4f4;
  color: #e0f1f4;
}
.booking.maintenance {
  background: rgb(150, 26, 4) !important;
  color: #fff;
  cursor: not-allowed;
}
.booking.trainer {
  background: rgb(21, 114, 2) !important;
  color: #fff;
  cursor: pointer;
}
.booking.dependency {
  background: #a79493 !important;
  cursor: not-allowed !important;
  border: 0 solid #e0f4f4;
  color: #fcf0f0;
}
.slot-plus .v-btn{
  width: 20px;
  height: 20px;
}
.capacity-based {
  width: 100%;
  display: flex;
  justify-content: center;
}
.capacity-based .plus-icon{
  width: 20px;
  height: 20px;
}
.capacity-based .attendance-box{
  min-width: 100px;
  margin-left: 10px;
  color: teal;
}
.booking.order-notes::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(45deg, black, transparent);
  width: 10px;
}
.booking.paid.credit::after {
  content: "c";
  position: absolute;
  top: 0px;
  right: 0px;
  background: #ffc000;
  width: 20px;
  height: 20px;
  text-align: center;
  border-radius: 0px;
}

</style>
